import '../models/user_model.dart';
import '../models/message_model.dart';
import '../services/firebase_service.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';

class DemoDataService {
  static const Uuid _uuid = Uuid();

  // إنشاء مستخدمين تجريبيين
  static Future<void> createDemoUsers() async {
    final demoUsers = [
      UserModel(
        id: 'user_ahmed',
        name: 'أحمد خالد',
        email: '<EMAIL>',
        avatarUrl: 'https://ui-avatars.com/api/?name=أحمد&background=10b981&color=fff',
        isOnline: true,
        lastSeen: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      UserModel(
        id: 'user_sara',
        name: 'سارة محمد',
        email: '<EMAIL>',
        avatarUrl: 'https://ui-avatars.com/api/?name=سارة&background=a855f7&color=fff',
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      UserModel(
        id: 'user_ali',
        name: 'علي حسن',
        email: '<EMAIL>',
        avatarUrl: 'https://ui-avatars.com/api/?name=علي&background=f59e0b&color=fff',
        isOnline: true,
        lastSeen: DateTime.now(),
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      UserModel(
        id: 'user_noor',
        name: 'نور عبدالله',
        email: '<EMAIL>',
        avatarUrl: 'https://ui-avatars.com/api/?name=نور&background=ec4899&color=fff',
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      UserModel(
        id: 'user_youssef',
        name: 'يوسف إبراهيم',
        email: '<EMAIL>',
        avatarUrl: 'https://ui-avatars.com/api/?name=يوسف&background=6366f1&color=fff',
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(hours: 5)),
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ];

    for (UserModel user in demoUsers) {
      await FirebaseService.createOrUpdateUser(user);
    }
  }

  // إنشاء محادثات تجريبية
  static Future<void> createDemoChats() async {
    String? currentUserId = FirebaseService.currentUserId;
    if (currentUserId == null) return;

    // محادثة مع أحمد
    String chatId1 = await FirebaseService.createChat([currentUserId, 'user_ahmed']);
    await _createDemoMessages(chatId1, currentUserId, 'user_ahmed', 'أحمد خالد');

    // محادثة مع سارة
    String chatId2 = await FirebaseService.createChat([currentUserId, 'user_sara']);
    await _createDemoMessages(chatId2, currentUserId, 'user_sara', 'سارة محمد');

    // محادثة مع علي
    String chatId3 = await FirebaseService.createChat([currentUserId, 'user_ali']);
    await _createDemoMessages(chatId3, currentUserId, 'user_ali', 'علي حسن');

    // محادثة مع نور
    String chatId4 = await FirebaseService.createChat([currentUserId, 'user_noor']);
    await _createDemoMessages(chatId4, currentUserId, 'user_noor', 'نور عبدالله');

    // محادثة مع يوسف
    String chatId5 = await FirebaseService.createChat([currentUserId, 'user_youssef']);
    await _createDemoMessages(chatId5, currentUserId, 'user_youssef', 'يوسف إبراهيم');
  }

  static Future<void> _createDemoMessages(
    String chatId,
    String currentUserId,
    String otherUserId,
    String otherUserName,
  ) async {
    final messages = _getDemoMessagesForUser(otherUserName);
    
    for (int i = 0; i < messages.length; i++) {
      final messageData = messages[i];
      final message = MessageModel(
        id: _uuid.v4(),
        chatId: chatId,
        senderId: messageData['sent'] ? currentUserId : otherUserId,
        senderName: messageData['sent'] ? 'محمد أحمد' : otherUserName,
        content: messageData['text'],
        type: MessageType.text,
        timestamp: DateTime.now().subtract(Duration(minutes: messages.length - i)),
        isRead: true,
        readBy: [currentUserId, otherUserId],
      );

      await FirebaseService.sendMessage(message);
      
      // تأخير قصير لضمان ترتيب الرسائل
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  static List<Map<String, dynamic>> _getDemoMessagesForUser(String userName) {
    switch (userName) {
      case 'أحمد خالد':
        return [
          {'text': 'مرحباً محمد، كيف حالك؟', 'sent': false},
          {'text': 'هل انتهيت من العمل على المشروع؟', 'sent': false},
          {'text': 'مرحباً أحمد، أنا بخير الحمد لله', 'sent': true},
          {'text': 'نعم، لقد انتهيت منه البارحة', 'sent': true},
          {'text': 'هذا رائع! هل يمكنك إرسال الملفات لي؟', 'sent': false},
          {'text': 'بالطبع، سأرسلها لك خلال ساعة', 'sent': true},
          {'text': 'نعم، سأكون هناك في الموعد المحدد', 'sent': false},
        ];
      case 'سارة محمد':
        return [
          {'text': 'مرحباً سارة، كيف كان اجتماع اليوم؟', 'sent': true},
          {'text': 'كان مفيد Berkshire Hathaway! هل يمكنك إرسال الملفات لي؟', 'sent': false},
          {'text': 'العفو، أي وقت', 'sent': true},
          {'text': 'شكراً لك على المساعدة', 'sent': false},
        ];
      case 'علي حسن':
        return [
          {'text': 'علي، هل يمكنك مراجعة التقرير؟', 'sent': true},
          {'text': 'بالطبع، سأراجعه اليوم', 'sent': false},
          {'text': 'هل انتهيت من المهمة؟', 'sent': false},
        ];
      case 'نور عبدالله':
        return [
          {'text': 'نور، متى ستكون الملفات جاهزة؟', 'sent': true},
          {'text': 'سأرسل لك الملفات لاحق Berkshire Hathaway! هل يمكنك إرسال الملفات لي؟', 'sent': false},
        ];
      case 'يوسف إبراهيم':
        return [
          {'text': 'مرحباً، كيف حالك؟', 'sent': false},
          {'text': 'أهلاً يوسف، أنا بخير', 'sent': true},
          {'text': 'هل لديك وقت للاجتماع غداً؟', 'sent': false},
        ];
      default:
        return [
          {'text': 'مرحباً', 'sent': false},
          {'text': 'أهلاً وسهلاً', 'sent': true},
        ];
    }
  }

  // إنشاء جميع البيانات التجريبية
  static Future<void> setupDemoData() async {
    try {
      debugPrint('إنشاء المستخدمين التجريبيين...');
      await createDemoUsers();
      
      debugPrint('إنشاء المحادثات التجريبية...');
      await createDemoChats();
      
      debugPrint('تم إنشاء البيانات التجريبية بنجاح!');
    } catch (e) {
      debugPrint('خطأ في إنشاء البيانات التجريبية: $e');
    }
  }
}


