<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Firebase Test</title>
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
</head>
<body>
    <h1>اختبار Firebase</h1>
    <div id="status">جاري التحميل...</div>
    <button onclick="testAuth()">اختبار المصادقة</button>
    <button onclick="testFirestore()">اختبار Firestore</button>

    <script>
        // Firebase Configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC5_TtUHCg8XdkbA7ooRnaisSytHyxHNWA",
            authDomain: "chat-app-arabic-508fb.firebaseapp.com",
            projectId: "chat-app-arabic-508fb",
            storageBucket: "chat-app-arabic-508fb.firebasestorage.app",
            messagingSenderId: "571841303318",
            appId: "1:571841303318:web:ae3c67fbaabc8e00fbf637"
        };

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            document.getElementById('status').innerHTML = '✅ تم تهيئة Firebase بنجاح';
            console.log('Firebase initialized successfully');
        } catch (error) {
            document.getElementById('status').innerHTML = '❌ خطأ في تهيئة Firebase: ' + error.message;
            console.error('Firebase initialization error:', error);
        }

        async function testAuth() {
            try {
                const result = await firebase.auth().signInAnonymously();
                document.getElementById('status').innerHTML = '✅ تم تسجيل الدخول كضيف: ' + result.user.uid;
                console.log('Anonymous sign-in successful:', result.user.uid);
            } catch (error) {
                document.getElementById('status').innerHTML = '❌ خطأ في المصادقة: ' + error.message;
                console.error('Auth error:', error);
            }
        }

        async function testFirestore() {
            try {
                const db = firebase.firestore();
                await db.collection('test').add({
                    message: 'Hello from Firebase!',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
                document.getElementById('status').innerHTML = '✅ تم إضافة بيانات إلى Firestore بنجاح';
                console.log('Firestore write successful');
            } catch (error) {
                document.getElementById('status').innerHTML = '❌ خطأ في Firestore: ' + error.message;
                console.error('Firestore error:', error);
            }
        }
    </script>
</body>
</html>
