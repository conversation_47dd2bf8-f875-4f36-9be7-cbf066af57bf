import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import '../services/local_data_service.dart';
import 'local_chat_screen.dart';
import 'package:intl/intl.dart';

class LocalChatListScreen extends StatefulWidget {
  const LocalChatListScreen({super.key});

  @override
  State<LocalChatListScreen> createState() => _LocalChatListScreenState();
}

class _LocalChatListScreenState extends State<LocalChatListScreen> {
  String searchQuery = '';
  List<ChatModel> filteredChats = [];
  List<ChatModel> allChats = [];

  @override
  void initState() {
    super.initState();
    _loadChats();
  }

  void _loadChats() {
    setState(() {
      allChats = LocalDataService.getUserChats(LocalDataService.currentUserId);
      filteredChats = allChats;
    });
  }

  void _filterChats(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        filteredChats = allChats;
      } else {
        filteredChats = allChats.where((chat) {
          UserModel? otherUser = _getOtherUser(chat);
          return chat.lastMessage.contains(query) ||
                 (otherUser?.name.contains(query) ?? false);
        }).toList();
      }
    });
  }

  UserModel? _getOtherUser(ChatModel chat) {
    String otherUserId = chat.participants
        .firstWhere((id) => id != LocalDataService.currentUserId, orElse: () => '');
    
    if (otherUserId.isEmpty) return null;
    return LocalDataService.getUser(otherUserId);
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(time);
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE', 'ar').format(time);
    } else {
      return DateFormat('dd/MM/yyyy').format(time);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB))),
              color: Colors.white,
            ),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundImage: NetworkImage(
                    'https://ui-avatars.com/api/?name=محمد&background=3b82f6&color=fff',
                  ),
                  radius: 24,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'محمد أحمد',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'متصل الآن',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    FontAwesomeIcons.ellipsisVertical,
                    color: Colors.grey,
                  ),
                  onPressed: () {},
                ),
              ],
            ),
          ),
          // Search
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              onChanged: _filterChats,
              decoration: InputDecoration(
                hintText: 'ابحث عن محادثة...',
                prefixIcon: const Icon(
                  FontAwesomeIcons.magnifyingGlass,
                  color: Colors.grey,
                  size: 18,
                ),
                filled: true,
                fillColor: const Color(0xFFF3F4F6),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          // Chat List
          Expanded(
            child: filteredChats.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.comments,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد محادثات',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: filteredChats.length,
                    separatorBuilder: (context, i) =>
                        const Divider(height: 1, color: Color(0xFFE5E7EB)),
                    itemBuilder: (context, i) {
                      final chat = filteredChats[i];
                      UserModel? otherUser = _getOtherUser(chat);
                      String userName = otherUser?.name ?? 'مستخدم';
                      String avatarUrl = otherUser?.avatarUrl ?? 
                          'https://ui-avatars.com/api/?name=$userName&background=6366f1&color=fff';
                      bool isOnline = otherUser?.isOnline ?? false;

                      int unreadCount = chat.getUnreadCountForUser(
                        LocalDataService.currentUserId
                      );

                      return Material(
                        color: Colors.white,
                        child: InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => LocalChatScreen(
                                  chat: chat,
                                  otherUser: otherUser,
                                  onBack: () => Navigator.pop(context),
                                ),
                              ),
                            ).then((_) => _loadChats()); // إعادة تحميل البيانات عند العودة
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 10,
                            ),
                            child: Row(
                              children: [
                                Stack(
                                  children: [
                                    CircleAvatar(
                                      backgroundImage: NetworkImage(avatarUrl),
                                      radius: 24,
                                    ),
                                    if (isOnline)
                                      Positioned(
                                        bottom: 0,
                                        right: 0,
                                        child: Container(
                                          width: 12,
                                          height: 12,
                                          decoration: BoxDecoration(
                                            color: Colors.green,
                                            border: Border.all(
                                              color: Colors.white,
                                              width: 2,
                                            ),
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                      ),
                                    if (unreadCount > 0)
                                      Positioned(
                                        top: -2,
                                        left: -2,
                                        child: Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Text(
                                            unreadCount.toString(),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 10,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            userName,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            _formatTime(chat.lastMessageTime),
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 2),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              chat.lastMessage.isEmpty 
                                                  ? 'لا توجد رسائل' 
                                                  : chat.lastMessage,
                                              style: const TextStyle(
                                                fontSize: 13,
                                                color: Colors.grey,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Icon(
                                            unreadCount > 0
                                                ? FontAwesomeIcons.check
                                                : FontAwesomeIcons.checkDouble,
                                            color: unreadCount > 0
                                                ? Colors.grey
                                                : Colors.blue,
                                            size: 14,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showNewChatDialog();
        },
        child: const Icon(FontAwesomeIcons.plus),
      ),
    );
  }

  void _showNewChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('محادثة جديدة'),
        content: const Text('تم إنشاء قاعدة البيانات بنجاح!\nيمكنك الآن إرسال واستقبال الرسائل.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
