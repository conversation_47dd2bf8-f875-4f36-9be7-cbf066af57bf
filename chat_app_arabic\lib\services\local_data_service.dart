import '../models/user_model.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';

class LocalDataService {
  // بيانات محلية للتجربة
  static final List<UserModel> _users = [
    UserModel(
      id: 'current_user',
      name: 'محمد أحمد',
      email: '<EMAIL>',
      avatarUrl: 'https://ui-avatars.com/api/?name=محمد&background=3b82f6&color=fff',
      isOnline: true,
      lastSeen: DateTime.now(),
      createdAt: DateTime.now(),
    ),
    UserModel(
      id: 'user_ahmed',
      name: 'أحمد خالد',
      email: '<EMAIL>',
      avatarUrl: 'https://ui-avatars.com/api/?name=أحمد&background=10b981&color=fff',
      isOnline: true,
      lastSeen: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    UserModel(
      id: 'user_sara',
      name: 'سارة محمد',
      email: '<EMAIL>',
      avatarUrl: 'https://ui-avatars.com/api/?name=سارة&background=a855f7&color=fff',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
    UserModel(
      id: 'user_ali',
      name: 'علي حسن',
      email: '<EMAIL>',
      avatarUrl: 'https://ui-avatars.com/api/?name=علي&background=f59e0b&color=fff',
      isOnline: true,
      lastSeen: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
    UserModel(
      id: 'user_noor',
      name: 'نور عبدالله',
      email: '<EMAIL>',
      avatarUrl: 'https://ui-avatars.com/api/?name=نور&background=ec4899&color=fff',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(days: 1)),
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
    ),
    UserModel(
      id: 'user_youssef',
      name: 'يوسف إبراهيم',
      email: '<EMAIL>',
      avatarUrl: 'https://ui-avatars.com/api/?name=يوسف&background=6366f1&color=fff',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(hours: 5)),
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
    ),
  ];

  static final List<ChatModel> _chats = [
    ChatModel(
      id: 'chat_1',
      participants: ['current_user', 'user_ahmed'],
      lastMessage: 'نعم، سأكون هناك في الموعد المحدد',
      lastMessageSenderId: 'user_ahmed',
      lastMessageTime: DateTime.now().subtract(const Duration(minutes: 30)),
      unreadCount: {'current_user': 0, 'user_ahmed': 0},
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
    ),
    ChatModel(
      id: 'chat_2',
      participants: ['current_user', 'user_sara'],
      lastMessage: 'شكراً لك على المساعدة',
      lastMessageSenderId: 'user_sara',
      lastMessageTime: DateTime.now().subtract(const Duration(days: 1)),
      unreadCount: {'current_user': 0, 'user_sara': 0},
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    ChatModel(
      id: 'chat_3',
      participants: ['current_user', 'user_ali'],
      lastMessage: 'هل انتهيت من المهمة؟',
      lastMessageSenderId: 'user_ali',
      lastMessageTime: DateTime.now().subtract(const Duration(days: 2)),
      unreadCount: {'current_user': 0, 'user_ali': 0},
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    ChatModel(
      id: 'chat_4',
      participants: ['current_user', 'user_noor'],
      lastMessage: 'سأرسل لك الملفات لاحقاً',
      lastMessageSenderId: 'user_noor',
      lastMessageTime: DateTime.now().subtract(const Duration(days: 3)),
      unreadCount: {'current_user': 0, 'user_noor': 0},
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    ChatModel(
      id: 'chat_5',
      participants: ['current_user', 'user_youssef'],
      lastMessage: 'مرحباً، كيف حالك؟',
      lastMessageSenderId: 'user_youssef',
      lastMessageTime: DateTime.now().subtract(const Duration(days: 4)),
      unreadCount: {'current_user': 3, 'user_youssef': 0},
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 4)),
    ),
  ];

  static final Map<String, List<MessageModel>> _messages = {
    'chat_1': [
      MessageModel(
        id: 'msg_1_1',
        chatId: 'chat_1',
        senderId: 'user_ahmed',
        senderName: 'أحمد خالد',
        content: 'مرحباً محمد، كيف حالك؟',
        type: MessageType.text,
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: true,
        readBy: ['current_user', 'user_ahmed'],
      ),
      MessageModel(
        id: 'msg_1_2',
        chatId: 'chat_1',
        senderId: 'current_user',
        senderName: 'محمد أحمد',
        content: 'مرحباً أحمد، أنا بخير الحمد لله',
        type: MessageType.text,
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 50)),
        isRead: true,
        readBy: ['current_user', 'user_ahmed'],
      ),
      MessageModel(
        id: 'msg_1_3',
        chatId: 'chat_1',
        senderId: 'user_ahmed',
        senderName: 'أحمد خالد',
        content: 'نعم، سأكون هناك في الموعد المحدد',
        type: MessageType.text,
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        isRead: true,
        readBy: ['current_user', 'user_ahmed'],
      ),
    ],
    'chat_2': [
      MessageModel(
        id: 'msg_2_1',
        chatId: 'chat_2',
        senderId: 'current_user',
        senderName: 'محمد أحمد',
        content: 'مرحباً سارة، كيف كان اجتماع اليوم؟',
        type: MessageType.text,
        timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
        isRead: true,
        readBy: ['current_user', 'user_sara'],
      ),
      MessageModel(
        id: 'msg_2_2',
        chatId: 'chat_2',
        senderId: 'user_sara',
        senderName: 'سارة محمد',
        content: 'شكراً لك على المساعدة',
        type: MessageType.text,
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
        readBy: ['current_user', 'user_sara'],
      ),
    ],
  };

  // الحصول على المستخدم الحالي
  static String get currentUserId => 'current_user';

  // الحصول على مستخدم بواسطة ID
  static UserModel? getUser(String userId) {
    try {
      return _users.firstWhere((user) => user.id == userId);
    } catch (e) {
      return null;
    }
  }

  // الحصول على محادثات المستخدم
  static List<ChatModel> getUserChats(String userId) {
    return _chats
        .where((chat) => chat.participants.contains(userId))
        .toList()
      ..sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
  }

  // الحصول على رسائل المحادثة
  static List<MessageModel> getChatMessages(String chatId) {
    return _messages[chatId] ?? [];
  }

  // إرسال رسالة جديدة
  static void sendMessage(MessageModel message) {
    // إضافة الرسالة إلى القائمة
    if (_messages[message.chatId] == null) {
      _messages[message.chatId] = [];
    }
    _messages[message.chatId]!.add(message);

    // تحديث آخر رسالة في المحادثة
    int chatIndex = _chats.indexWhere((chat) => chat.id == message.chatId);
    if (chatIndex != -1) {
      _chats[chatIndex] = _chats[chatIndex].copyWith(
        lastMessage: message.content,
        lastMessageSenderId: message.senderId,
        lastMessageTime: message.timestamp,
        updatedAt: message.timestamp,
      );
    }
  }

  // تحديد الرسائل كمقروءة
  static void markMessagesAsRead(String chatId, String userId) {
    int chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
    if (chatIndex != -1) {
      Map<String, int> newUnreadCount = Map.from(_chats[chatIndex].unreadCount);
      newUnreadCount[userId] = 0;
      _chats[chatIndex] = _chats[chatIndex].copyWith(unreadCount: newUnreadCount);
    }
  }
}
