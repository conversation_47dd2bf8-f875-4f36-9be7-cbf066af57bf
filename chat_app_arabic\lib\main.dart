import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'services/demo_data_service.dart';
import 'models/user_model.dart';
import 'screens/chat_list_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تهيئة Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('✅ تم تهيئة Firebase بنجاح');

    // تجربة تسجيل الدخول كضيف (اختياري)
    try {
      await FirebaseService.signInAnonymously();
      debugPrint('✅ تم تسجيل الدخول كضيف');

      // إنشاء مستخدم تجريبي
      if (FirebaseService.currentUserId != null) {
        await FirebaseService.createOrUpdateUser(
          UserModel(
            id: FirebaseService.currentUserId!,
            name: 'محمد أحمد',
            email: '<EMAIL>',
            avatarUrl:
                'https://ui-avatars.com/api/?name=محمد&background=3b82f6&color=fff',
            isOnline: true,
            lastSeen: DateTime.now(),
            createdAt: DateTime.now(),
          ),
        );
        debugPrint('✅ تم إنشاء المستخدم التجريبي');

        // إنشاء البيانات التجريبية
        await DemoDataService.setupDemoData();
        debugPrint('✅ تم إنشاء البيانات التجريبية');
      }
    } catch (authError) {
      debugPrint('⚠️ تعذر تسجيل الدخول: $authError');
      debugPrint('📱 سيعمل التطبيق بالبيانات المحلية');
    }
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة Firebase: $e');
    debugPrint('📱 سيعمل التطبيق بالبيانات المحلية');
  }

  runApp(const ChatApp());
}

class ChatApp extends StatelessWidget {
  const ChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق المراسلة العربي',
      theme: ThemeData(
        fontFamily: 'Arial',
        primarySwatch: Colors.blue,
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: 'Arial'),
          bodyMedium: TextStyle(fontFamily: 'Arial'),
          displayLarge: TextStyle(fontFamily: 'Arial'),
          displayMedium: TextStyle(fontFamily: 'Arial'),
          displaySmall: TextStyle(fontFamily: 'Arial'),
          headlineLarge: TextStyle(fontFamily: 'Arial'),
          headlineMedium: TextStyle(fontFamily: 'Arial'),
          headlineSmall: TextStyle(fontFamily: 'Arial'),
          titleLarge: TextStyle(fontFamily: 'Arial'),
          titleMedium: TextStyle(fontFamily: 'Arial'),
          titleSmall: TextStyle(fontFamily: 'Arial'),
          labelLarge: TextStyle(fontFamily: 'Arial'),
          labelMedium: TextStyle(fontFamily: 'Arial'),
          labelSmall: TextStyle(fontFamily: 'Arial'),
          bodySmall: TextStyle(fontFamily: 'Arial'),
        ),
      ),
      debugShowCheckedModeBanner: false,
      home: const ChatListScreen(),
      locale: const Locale('ar', 'EG'),
      supportedLocales: const [Locale('ar', 'EG'), Locale('en', 'US')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
    );
  }
}
