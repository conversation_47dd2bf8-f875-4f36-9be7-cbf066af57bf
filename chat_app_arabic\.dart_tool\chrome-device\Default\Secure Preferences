{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "‏يمكنك اكتشاف تطبيقات، وألعاب، وإضافات، ومظاهر رائعة لمتصفح Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "السوق الإلكتروني", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "FB2986C95C9701053E6EDD53B783998334BF663D22816F3F9948CB5D05643B4D"}, "extensions": {"ui": {"developer_mode": "3CFBE056AD0D2ADD63D7FDD32C51DD96390D7424C7156F956B5A3C6A8543C6D4"}}, "homepage": "450A1C8D52FBCB34E58885C3C1BC9C19DA7C2160F73AB35F56C49C4192081EB1", "homepage_is_newtabpage": "8CECDAD360F426472E73A20249D88CDDC069C0DC83B76AFE68C9FBAA62CCA4FD", "session": {"restore_on_startup": "BD14FCAE6D946AA4CB778BF4C41549B4D28172ADDE289880BCF4DAFF44512144", "startup_urls": "7E4A6C81A4BD2FED9F79B25895B39063E6D94E48B6EC4100BAC3D21B4AB49FB3"}}, "browser": {"show_home_button": "A70856D5E75C9B2CA6721A7417C41B64090A300E0CEA49C45D2C62A804B660FE"}, "default_search_provider_data": {"template_url_data": "431E487303EEB764490A114F612445CBAB8AD8A7B36B4D5EF18B5C87AA3CBDE7"}, "enterprise_signin": {"policy_recovery_token": "DC811474FE6A29055A8FAED5E39145B0779669DA7E4C43CAC2F5D29967FC4C3B"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "5328EC3905C268CB61F07623454D017A9C471B579318868F5F9BCEB916B29ED4", "fignfifoniblkonapihmkfakmlgkbkcf": "A03741E7B42A0CA91F5467E7A62E355C2903FAA9E4455C31E685E6C9966C8124", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "7CD8750D25FCB477E795EAFED3FD5DEE9DCB89455F6ADC43A8279E62697BA6E7", "nkeimhogjdpnpccoofpliimaahmaaome": "0E1E732928980E3E323935B0B1A32A2931681881AEA02D3BFDE990E7EDD4323E"}, "ui": {"developer_mode": "E3E8B7636A3C4AEB0B9EF03F640FC0AE11A792DC8F1980D63018EACE3C88EEB2"}}, "google": {"services": {"account_id": "40D17B9F4CE9AC0FE96F1FE12ADF79A6A85AB48761A4A4C4C782E14A35B64C95", "last_signed_in_username": "C8D77E5C79986096DE59C03E12C31E89DD5E173A625B2F4A9F4855E470AAA91D", "last_username": "D652BE41EE8FFF0578BFF7AE1CDFD430295CC1B97124F1118047E22D7AEAA5AC"}}, "homepage": "C55072387A20E2A25F014D5A8FEC40114D160AF661BF4696391CE723D7658F40", "homepage_is_newtabpage": "3273628D4482B1C28A020163B6EC052164A57DF811A4D234893FA9A4B3B95223", "media": {"cdm": {"origin_data": "BF12FF98CA15858CDB51E59B2307138D9A7F01B125FC2C126709A18230E83370"}, "storage_id_salt": "19B31774C783DF50F39FE98098D073BFE0592E79F93B9F2757ED33B2455FB970"}, "module_blocklist_cache_md5_digest": "03D499E6A8958435EA9C1434D7E7F82702481070BF5610F34BA692FB1542BFD6", "pinned_tabs": "47CDE2CC0AA8342005448F3AAF60123A875692595D43D658562FC63D30ABC290", "prefs": {"preference_reset_time": "18756F3295BCBA2842FBC6D3729594F2DE4515ABDE4290F33CA68125BF4766DE"}, "safebrowsing": {"incidents_sent": "4022E5D8BD8FDCF8A81F95A2E17FFDCE74B7CEAE1D17D9B0912EF21EC86B1B40"}, "search_provider_overrides": "BDF200F266D6B2838546CCEA3DA19F2D37A5637ECF46E6E11B059992C70390C7", "session": {"restore_on_startup": "74277A76E317F74891D22DEF3EFA05D68A3C6FB0818E8F2D2840BD3317BED1FE", "startup_urls": "3BDFF889CDCD4EF054D9C7BB7238939531E8A2809E8461FD91A57487E3260E67"}}, "super_mac": "AA9D49B61785E8AD20DF133D9665A4CCD2FD331B0D581EF663670B438F0C0537"}}