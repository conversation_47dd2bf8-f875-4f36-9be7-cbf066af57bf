import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import '../models/user_model.dart';
import '../models/chat_model.dart';
import '../models/message_model.dart';

class FirebaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 50,
      colors: true,
      printEmojis: true,
    ),
  );

  // Collections
  static const String usersCollection = 'users';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';

  // Proper logging implementation
  static void _logError(String message, dynamic error) {
    _logger.e(message, error: error, stackTrace: StackTrace.current);
  }

  // الحصول على المستخدم الحالي
  static User? get currentUser => _auth.currentUser;
  static String? get currentUserId => _auth.currentUser?.uid;

  // تسجيل الدخول كضيف (للتجربة)
  static Future<UserCredential> signInAnonymously() async {
    try {
      return await _auth.signInAnonymously();
    } catch (e) {
      // Replace print with proper logging
      _logError('خطأ في تسجيل الدخول', e);
      rethrow;
    }
  }

  // إنشاء أو تحديث مستخدم
  static Future<void> createOrUpdateUser(UserModel user) async {
    try {
      await _firestore
          .collection(usersCollection)
          .doc(user.id)
          .set(user.toFirestore(), SetOptions(merge: true));
    } catch (e) {
      _logError('خطأ في إنشاء المستخدم', e);
      rethrow;
    }
  }

  // الحصول على مستخدم بواسطة ID
  static Future<UserModel?> getUser(String userId) async {
    DocumentSnapshot doc =
        await _firestore.collection(usersCollection).doc(userId).get();

    if (doc.exists) {
      return UserModel.fromFirestore(doc);
    }
    return null;
  }

  // الحصول على جميع المستخدمين
  static Stream<List<UserModel>> getAllUsers() {
    return _firestore
        .collection(usersCollection)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList(),
        );
  }

  // تحديث حالة الاتصال للمستخدم
  static Future<void> updateUserOnlineStatus(
    String userId,
    bool isOnline,
  ) async {
    await _firestore.collection(usersCollection).doc(userId).update({
      'isOnline': isOnline,
      'lastSeen': Timestamp.now(),
    });
  }

  // إنشاء محادثة جديدة
  static Future<String> createChat(List<String> participants) async {
    // التحقق من وجود محادثة بين نفس المشاركين
    QuerySnapshot existingChats =
        await _firestore
            .collection(chatsCollection)
            .where('participants', arrayContainsAny: participants)
            .get();

    for (QueryDocumentSnapshot doc in existingChats.docs) {
      List<String> chatParticipants = List<String>.from(doc['participants']);
      if (chatParticipants.length == participants.length &&
          chatParticipants.every((p) => participants.contains(p))) {
        return doc.id;
      }
    }

    // إنشاء محادثة جديدة
    DocumentReference chatRef = await _firestore
        .collection(chatsCollection)
        .add({
          'participants': participants,
          'lastMessage': '',
          'lastMessageSenderId': '',
          'lastMessageTime': Timestamp.now(),
          'unreadCount': {for (String p in participants) p: 0},
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        });

    return chatRef.id;
  }

  // الحصول على محادثات المستخدم
  static Stream<List<ChatModel>> getUserChats(String userId) {
    return _firestore
        .collection(chatsCollection)
        .where('participants', arrayContains: userId)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => ChatModel.fromFirestore(doc)).toList(),
        );
  }

  // إرسال رسالة
  static Future<void> sendMessage(MessageModel message) async {
    // إضافة الرسالة
    await _firestore.collection(messagesCollection).add(message.toFirestore());

    // تحديث آخر رسالة في المحادثة
    await _firestore.collection(chatsCollection).doc(message.chatId).update({
      'lastMessage': message.content,
      'lastMessageSenderId': message.senderId,
      'lastMessageTime': message.timestamp,
      'updatedAt': Timestamp.now(),
    });

    // تحديث عدد الرسائل غير المقروءة
    DocumentSnapshot chatDoc =
        await _firestore.collection(chatsCollection).doc(message.chatId).get();

    if (chatDoc.exists) {
      ChatModel chat = ChatModel.fromFirestore(chatDoc);
      Map<String, int> newUnreadCount = Map.from(chat.unreadCount);

      for (String participantId in chat.participants) {
        if (participantId != message.senderId) {
          newUnreadCount[participantId] =
              (newUnreadCount[participantId] ?? 0) + 1;
        }
      }

      await _firestore.collection(chatsCollection).doc(message.chatId).update({
        'unreadCount': newUnreadCount,
      });
    }
  }

  // الحصول على رسائل المحادثة
  static Stream<List<MessageModel>> getChatMessages(String chatId) {
    return _firestore
        .collection(messagesCollection)
        .where('chatId', isEqualTo: chatId)
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => MessageModel.fromFirestore(doc))
                  .toList(),
        );
  }

  // تحديد الرسائل كمقروءة
  static Future<void> markMessagesAsRead(String chatId, String userId) async {
    // تحديث عدد الرسائل غير المقروءة في المحادثة
    await _firestore.collection(chatsCollection).doc(chatId).update({
      'unreadCount.$userId': 0,
    });
  }
}
