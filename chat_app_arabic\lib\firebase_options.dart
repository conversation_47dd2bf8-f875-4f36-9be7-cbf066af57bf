// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC5_TtUHCg8XdkbA7ooRnaisSytHyxHNWA',
    appId: '1:571841303318:web:ae3c67fbaabc8e00fbf637',
    messagingSenderId: '571841303318',
    projectId: 'chat-app-arabic-508fb',
    authDomain: 'chat-app-arabic-508fb.firebaseapp.com',
    storageBucket: 'chat-app-arabic-508fb.firebasestorage.app',
    measurementId: 'G-4P9YRZ06E4',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC5_TtUHCg8XdkbA7ooRnaisSytHyxHNWA',
    appId: '1:571841303318:android:b94131bd56dbc46bfbf637',
    messagingSenderId: '571841303318',
    projectId: 'chat-app-arabic-508fb',
    storageBucket: 'chat-app-arabic-508fb.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC5_TtUHCg8XdkbA7ooRnaisSytHyxHNWA',
    appId: '1:571841303318:ios:b94131bd56dbc46bfbf637',
    messagingSenderId: '571841303318',
    projectId: 'chat-app-arabic-508fb',
    storageBucket: 'chat-app-arabic-508fb.firebasestorage.app',
    iosBundleId: 'com.example.chatAppArabic',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC5_TtUHCg8XdkbA7ooRnaisSytHyxHNWA',
    appId: '1:571841303318:macos:b94131bd56dbc46bfbf637',
    messagingSenderId: '571841303318',
    projectId: 'chat-app-arabic-508fb',
    storageBucket: 'chat-app-arabic-508fb.firebasestorage.app',
    iosBundleId: 'com.example.chatAppArabic',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyC5_TtUHCg8XdkbA7ooRnaisSytHyxHNWA',
    appId: '1:571841303318:windows:b94131bd56dbc46bfbf637',
    messagingSenderId: '571841303318',
    projectId: 'chat-app-arabic-508fb',
    storageBucket: 'chat-app-arabic-508fb.firebasestorage.app',
  );
}
